.App {
  text-align: center;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
}

.App-header h1 {
  margin: 0 0 20px 0;
  font-size: 1.8rem;
}

.map-controls {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
  margin-top: 15px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.control-group label {
  font-weight: 500;
  font-size: 0.9rem;
}

.control-group select {
  padding: 5px 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  font-size: 0.9rem;
}

.control-group input[type="checkbox"] {
  margin-right: 5px;
}

.map-container {
  flex: 1;
  padding: 20px;
  background-color: #f5f5f5;
}

.App-footer {
  background-color: #f8f9fa;
  padding: 15px;
  border-top: 1px solid #dee2e6;
  color: #6c757d;
  font-size: 0.9rem;
}

.App-footer p {
  margin: 5px 0;
}

.App-footer a {
  color: #007bff;
  text-decoration: none;
}

.App-footer a:hover {
  text-decoration: underline;
}

/* Responsive design */
@media (max-width: 768px) {
  .App-header h1 {
    font-size: 1.4rem;
  }
  
  .map-controls {
    flex-direction: column;
    gap: 15px;
  }
  
  .control-group {
    justify-content: center;
  }
  
  .map-container {
    padding: 10px;
  }
}
