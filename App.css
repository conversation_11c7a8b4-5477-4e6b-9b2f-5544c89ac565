.App {
  text-align: center;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
}

.App-header h1 {
  margin: 0 0 20px 0;
  font-size: 1.8rem;
}

.map-controls {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
  margin-top: 15px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.control-group label {
  font-weight: 500;
  font-size: 0.9rem;
}

.control-group select {
  padding: 5px 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  font-size: 0.9rem;
}

.control-group input[type="checkbox"] {
  margin-right: 5px;
}

.map-container {
  flex: 1;
  padding: 20px;
  background-color: #f5f5f5;
}

.App-footer {
  background-color: #f8f9fa;
  padding: 15px;
  border-top: 1px solid #dee2e6;
  color: #6c757d;
  font-size: 0.9rem;
}

.App-footer p {
  margin: 5px 0;
}

.App-footer a {
  color: #007bff;
  text-decoration: none;
}

.App-footer a:hover {
  text-decoration: underline;
}

/* Styles pour la démo hors ligne */
.color-controls {
  margin-top: 20px;
  padding: 15px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.color-controls h3 {
  margin: 0 0 15px 0;
  font-size: 1.1rem;
}

.color-presets {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  flex-wrap: wrap;
  justify-content: center;
}

.preset-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 500;
  transition: transform 0.2s;
}

.preset-btn:hover {
  transform: scale(1.05);
}

.preset-btn.ocean {
  background: linear-gradient(45deg, #0ea5e9, #84cc16);
  color: white;
}

.preset-btn.classic {
  background: linear-gradient(45deg, #64748b, #f59e0b);
  color: white;
}

.preset-btn.night {
  background: linear-gradient(45deg, #1e293b, #475569);
  color: white;
}

.preset-btn.earth {
  background: linear-gradient(45deg, #0369a1, #65a30d);
  color: white;
}

.color-inputs {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  justify-content: center;
}

.color-input-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.color-input-group label {
  font-size: 0.8rem;
  font-weight: 500;
}

.color-input-group input[type="color"] {
  width: 40px;
  height: 40px;
  border: 2px solid white;
  border-radius: 50%;
  cursor: pointer;
  background: none;
}

.offline-info {
  margin-bottom: 15px;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 5px;
}

.offline-info h4 {
  margin: 0 0 10px 0;
}

.connection-status {
  margin-bottom: 10px;
}

.online {
  color: #28a745;
  font-weight: bold;
}

.offline {
  color: #dc3545;
  font-weight: bold;
}

/* Responsive design */
@media (max-width: 768px) {
  .App-header h1 {
    font-size: 1.4rem;
  }

  .map-controls {
    flex-direction: column;
    gap: 15px;
  }

  .control-group {
    justify-content: center;
  }

  .map-container {
    padding: 10px;
  }

  .color-presets {
    flex-direction: column;
    align-items: center;
  }

  .color-inputs {
    flex-direction: column;
    align-items: center;
  }
}
