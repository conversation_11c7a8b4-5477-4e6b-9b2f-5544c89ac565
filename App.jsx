import React, { useState } from 'react';
import WorldMapComponent from './WorldMapComponent';
import './App.css';

function App() {
  const [mapStyle, setMapStyle] = useState('simplified1');
  const [showMoroccoHighlight, setShowMoroccoHighlight] = useState(true);

  return (
    <div className="App">
      <header className="App-header">
        <h1>Carte du Monde avec Frontières Correctes du Maroc</h1>
        
        {/* Contrôles pour la carte */}
        <div className="map-controls">
          <div className="control-group">
            <label htmlFor="map-style">Style de carte:</label>
            <select 
              id="map-style"
              value={mapStyle} 
              onChange={(e) => setMapStyle(e.target.value)}
            >
              <option value="detailed">Détaillée</option>
              <option value="simplified1">Simplifiée 1</option>
              <option value="simplified2">Simplifiée 2</option>
            </select>
          </div>
          
          <div className="control-group">
            <label>
              <input
                type="checkbox"
                checked={showMoroccoHighlight}
                onChange={(e) => setShowMoroccoHighlight(e.target.checked)}
              />
              Mettre en évidence le Maroc
            </label>
          </div>
        </div>
      </header>

      <main className="map-container">
        <WorldMapComponent
          height="600px"
          width="100%"
          mapStyle={mapStyle}
          showMoroccoHighlight={showMoroccoHighlight}
        />
      </main>

      <footer className="App-footer">
        <p>
          Données cartographiques avec les frontières correctes du Maroc
        </p>
        <p>
          Créé par Mahmoud Zakaria - 
          <a href="https://www.mahmoud.ma/" target="_blank" rel="noopener noreferrer">
            www.mahmoud.ma
          </a>
        </p>
      </footer>
    </div>
  );
}

export default App;
