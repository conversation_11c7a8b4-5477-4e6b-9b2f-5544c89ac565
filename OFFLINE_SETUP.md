# Guide d'Installation - Mode Hors Ligne

Ce guide vous explique comment utiliser la carte du monde **sans connexion internet**.

## 🌍 Solutions Disponibles

### Solution 1: Mode Hors Ligne Simple (Recommandée)
Utilise uniquement les données GeoJSON sans fond de carte.

### Solution 2: Composant Entièrement Hors Ligne
Composant optimisé spécialement pour l'utilisation hors ligne.

## 📋 Prérequis

1. **Fichiers GeoJSON** : Vos fichiers de données cartographiques
2. **React + Leaflet** : Déjà installés
3. **Serveur local** : Pour servir les fichiers (même hors ligne)

## 🚀 Installation Rapide

### Étape 1: Structure des Fichiers
```
votre-projet/
├── public/
│   └── data/
│       ├── world_map_detailed.geojson
│       ├── world_map_simplified_1.geojson
│       └── world_map_simplified_2.geojson
├── src/
│   ├── WorldMapComponent.jsx (mode mixte)
│   ├── OfflineWorldMapComponent.jsx (100% hors ligne)
│   └── ...
```

### Étape 2: Copier les Fichiers GeoJSON
```bash
# Créer le dossier data dans public
mkdir public/data

# Copier vos fichiers GeoJSON
cp morocco-complete-map-geojson-topojson-main/world_map_*.geojson public/data/
```

### Étape 3: Utilisation

#### Option A: Composant Mixte (avec option hors ligne)
```jsx
import WorldMapComponent from './WorldMapComponent';

<WorldMapComponent
  height="600px"
  width="100%"
  mapStyle="simplified1"
  offlineMode={true}  // ← Active le mode hors ligne
  backgroundColor="#87CEEB"
/>
```

#### Option B: Composant 100% Hors Ligne
```jsx
import OfflineWorldMapComponent from './OfflineWorldMapComponent';

<OfflineWorldMapComponent
  height="600px"
  width="100%"
  mapStyle="simplified1"
  showMoroccoHighlight={true}
  backgroundColor="#4A90E2"
  landColor="#2ECC71"
  moroccoColor="#E74C3C"
/>
```

## 🎨 Personnalisation Hors Ligne

### Couleurs Personnalisées
```jsx
<OfflineWorldMapComponent
  backgroundColor="#1e3a8a"    // Océan bleu foncé
  landColor="#22c55e"          // Terre verte
  moroccoColor="#dc2626"       // Maroc rouge
  borderColor="#374151"        // Bordures grises
/>
```

### Styles Prédéfinis
```jsx
// Style "Terre et Mer"
backgroundColor="#0ea5e9"  // Bleu océan
landColor="#84cc16"        // Vert terre

// Style "Classique"
backgroundColor="#64748b"  // Gris océan
landColor="#f59e0b"        // Orange terre

// Style "Nuit"
backgroundColor="#1e293b"  // Bleu très foncé
landColor="#475569"        // Gris foncé
```

## 🔧 Fonctionnalités Hors Ligne

### ✅ Ce qui fonctionne sans internet :
- ✅ Affichage de la carte complète
- ✅ Zoom et navigation
- ✅ Survol des pays
- ✅ Popups avec noms des pays
- ✅ Mise en évidence du Maroc
- ✅ Changement de style de carte
- ✅ Légende interactive

### ❌ Ce qui nécessite internet :
- ❌ Fond de carte satellite/terrain
- ❌ Géocodage d'adresses
- ❌ Données en temps réel

## 🚀 Démarrage Hors Ligne

### 1. Développement Local
```bash
npm run dev
# ou
yarn dev
```
Votre application sera accessible sur `http://localhost:3000` même sans internet.

### 2. Build pour Production
```bash
npm run build
# ou
yarn build
```

### 3. Servir en Local (Production)
```bash
npm run preview
# ou
yarn preview
```

## 📱 Utilisation Mobile Hors Ligne

Pour une utilisation sur mobile/tablette sans internet :

1. **Construisez l'application** : `npm run build`
2. **Servez localement** : Utilisez un serveur HTTP simple
3. **Accédez via IP locale** : `http://192.168.1.X:3000`

### Serveur HTTP Simple
```bash
# Avec Python
cd dist
python -m http.server 3000

# Avec Node.js
npx serve dist -p 3000

# Avec PHP
cd dist
php -S localhost:3000
```

## 🔍 Dépannage

### Problème: "Fichier GeoJSON non trouvé"
**Solution :**
1. Vérifiez que les fichiers sont dans `public/data/`
2. Vérifiez les noms de fichiers (sensible à la casse)
3. Redémarrez le serveur de développement

### Problème: "Carte vide"
**Solution :**
1. Activez le mode hors ligne : `offlineMode={true}`
2. Vérifiez la console pour les erreurs
3. Testez avec un fichier GeoJSON plus simple

### Problème: "Performance lente"
**Solution :**
1. Utilisez `world_map_simplified_2.geojson` (plus léger)
2. Réduisez la `fillOpacity`
3. Désactivez les animations de survol

## 📊 Tailles des Fichiers

| Fichier | Taille | Détail | Recommandation |
|---------|--------|--------|----------------|
| `world_map_detailed.geojson` | ~5MB | Très détaillé | Connexion rapide |
| `world_map_simplified_1.geojson` | ~1MB | Équilibré | **Recommandé** |
| `world_map_simplified_2.geojson` | ~500KB | Léger | Mobile/Lent |

## 🎯 Cas d'Usage Hors Ligne

- 📱 **Applications mobiles** sans données
- 🏢 **Présentations** en entreprise
- 🎓 **Éducation** en classe sans WiFi
- 🚗 **Applications embarquées**
- 🏠 **Démonstrations** à domicile

## 💡 Conseils d'Optimisation

1. **Préchargez les données** au démarrage
2. **Utilisez la version simplifiée** pour de meilleures performances
3. **Cachez les données** dans localStorage si nécessaire
4. **Testez sur différents appareils** avant déploiement

---

**Résultat :** Une carte du monde entièrement fonctionnelle sans connexion internet ! 🌍✨
