import React, { useState } from 'react';
import WorldMapComponent from './WorldMapComponent';
import OfflineWorldMapComponent from './OfflineWorldMapComponent';
import './App.css';

function OfflineDemo() {
  const [componentType, setComponentType] = useState('mixed'); // 'mixed' ou 'offline'
  const [mapStyle, setMapStyle] = useState('simplified1');
  const [showMoroccoHighlight, setShowMoroccoHighlight] = useState(true);
  const [offlineMode, setOfflineMode] = useState(true);
  
  // Couleurs personnalisables pour le mode hors ligne
  const [colors, setColors] = useState({
    backgroundColor: '#4A90E2',
    landColor: '#2ECC71',
    moroccoColor: '#E74C3C',
    borderColor: '#34495E'
  });

  const colorPresets = {
    ocean: {
      backgroundColor: '#0ea5e9',
      landColor: '#84cc16',
      moroccoColor: '#dc2626',
      borderColor: '#374151'
    },
    classic: {
      backgroundColor: '#64748b',
      landColor: '#f59e0b',
      moroccoColor: '#dc2626',
      borderColor: '#1f2937'
    },
    night: {
      backgroundColor: '#1e293b',
      landColor: '#475569',
      moroccoColor: '#ef4444',
      borderColor: '#64748b'
    },
    earth: {
      backgroundColor: '#0369a1',
      landColor: '#65a30d',
      moroccoColor: '#dc2626',
      borderColor: '#374151'
    }
  };

  const applyColorPreset = (presetName) => {
    setColors(colorPresets[presetName]);
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>🌍 Carte du Monde - Mode Hors Ligne</h1>
        <p>Fonctionne sans connexion internet !</p>
        
        {/* Sélecteur de composant */}
        <div className="map-controls">
          <div className="control-group">
            <label htmlFor="component-type">Type de composant:</label>
            <select 
              id="component-type"
              value={componentType} 
              onChange={(e) => setComponentType(e.target.value)}
            >
              <option value="mixed">Composant Mixte</option>
              <option value="offline">Composant 100% Hors Ligne</option>
            </select>
          </div>

          <div className="control-group">
            <label htmlFor="map-style">Style de carte:</label>
            <select 
              id="map-style"
              value={mapStyle} 
              onChange={(e) => setMapStyle(e.target.value)}
            >
              <option value="detailed">Détaillée</option>
              <option value="simplified1">Simplifiée 1</option>
              <option value="simplified2">Simplifiée 2</option>
            </select>
          </div>
          
          <div className="control-group">
            <label>
              <input
                type="checkbox"
                checked={showMoroccoHighlight}
                onChange={(e) => setShowMoroccoHighlight(e.target.checked)}
              />
              Mettre en évidence le Maroc
            </label>
          </div>

          {componentType === 'mixed' && (
            <div className="control-group">
              <label>
                <input
                  type="checkbox"
                  checked={offlineMode}
                  onChange={(e) => setOfflineMode(e.target.checked)}
                />
                Mode hors ligne
              </label>
            </div>
          )}
        </div>

        {/* Contrôles de couleur pour le mode hors ligne */}
        {(componentType === 'offline' || (componentType === 'mixed' && offlineMode)) && (
          <div className="color-controls">
            <h3>🎨 Personnalisation des Couleurs</h3>
            
            {/* Presets de couleurs */}
            <div className="color-presets">
              <button onClick={() => applyColorPreset('ocean')} className="preset-btn ocean">
                Océan
              </button>
              <button onClick={() => applyColorPreset('classic')} className="preset-btn classic">
                Classique
              </button>
              <button onClick={() => applyColorPreset('night')} className="preset-btn night">
                Nuit
              </button>
              <button onClick={() => applyColorPreset('earth')} className="preset-btn earth">
                Terre
              </button>
            </div>

            {/* Contrôles de couleur individuels */}
            <div className="color-inputs">
              <div className="color-input-group">
                <label>Océan:</label>
                <input
                  type="color"
                  value={colors.backgroundColor}
                  onChange={(e) => setColors({...colors, backgroundColor: e.target.value})}
                />
              </div>
              <div className="color-input-group">
                <label>Terre:</label>
                <input
                  type="color"
                  value={colors.landColor}
                  onChange={(e) => setColors({...colors, landColor: e.target.value})}
                />
              </div>
              <div className="color-input-group">
                <label>Maroc:</label>
                <input
                  type="color"
                  value={colors.moroccoColor}
                  onChange={(e) => setColors({...colors, moroccoColor: e.target.value})}
                />
              </div>
              <div className="color-input-group">
                <label>Bordures:</label>
                <input
                  type="color"
                  value={colors.borderColor}
                  onChange={(e) => setColors({...colors, borderColor: e.target.value})}
                />
              </div>
            </div>
          </div>
        )}
      </header>

      <main className="map-container">
        {componentType === 'mixed' ? (
          <WorldMapComponent
            height="600px"
            width="100%"
            mapStyle={mapStyle}
            showMoroccoHighlight={showMoroccoHighlight}
            offlineMode={offlineMode}
            backgroundColor={colors.backgroundColor}
          />
        ) : (
          <OfflineWorldMapComponent
            height="600px"
            width="100%"
            mapStyle={mapStyle}
            showMoroccoHighlight={showMoroccoHighlight}
            backgroundColor={colors.backgroundColor}
            landColor={colors.landColor}
            moroccoColor={colors.moroccoColor}
            borderColor={colors.borderColor}
          />
        )}
      </main>

      <footer className="App-footer">
        <div className="offline-info">
          <h4>📡 Statut de Connexion</h4>
          <div className="connection-status">
            {navigator.onLine ? (
              <span className="online">🟢 En ligne</span>
            ) : (
              <span className="offline">🔴 Hors ligne</span>
            )}
          </div>
          <p>
            Cette carte fonctionne parfaitement même sans connexion internet !
          </p>
        </div>
        
        <p>
          Données cartographiques avec les frontières correctes du Maroc
        </p>
        <p>
          Créé par Mahmoud Zakaria - 
          <a href="https://www.mahmoud.ma/" target="_blank" rel="noopener noreferrer">
            www.mahmoud.ma
          </a>
        </p>
      </footer>
    </div>
  );
}

export default OfflineDemo;
