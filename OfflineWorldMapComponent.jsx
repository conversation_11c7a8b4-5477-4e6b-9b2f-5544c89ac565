import React, { useEffect, useRef, useState } from 'react';
import { MapContainer, GeoJSON } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';

const OfflineWorldMapComponent = ({ 
  height = '500px', 
  width = '100%',
  mapStyle = 'simplified1',
  showMoroccoHighlight = true,
  backgroundColor = '#4A90E2', // Couleur océan
  landColor = '#2ECC71', // Couleur terre
  moroccoColor = '#E74C3C', // Couleur Maroc
  borderColor = '#34495E' // Couleur bordures
}) => {
  const [geoJsonData, setGeoJsonData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fonction pour charger les données GeoJSON
  const loadGeoJsonData = async () => {
    try {
      setLoading(true);
      let fileName;
      
      switch (mapStyle) {
        case 'detailed':
          fileName = 'world_map_detailed.geojson';
          break;
        case 'simplified1':
          fileName = 'world_map_simplified_1.geojson';
          break;
        case 'simplified2':
          fileName = 'world_map_simplified_2.geojson';
          break;
        default:
          fileName = 'world_map_simplified_1.geojson';
      }

      const response = await fetch(`/data/${fileName}`);
      
      if (!response.ok) {
        throw new Error(`Erreur lors du chargement: ${response.status}`);
      }
      
      const data = await response.json();
      setGeoJsonData(data);
    } catch (err) {
      setError(err.message);
      console.error('Erreur lors du chargement des données GeoJSON:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadGeoJsonData();
  }, [mapStyle]);

  // Style optimisé pour le mode hors ligne
  const countryStyle = (feature) => {
    const isMorocco = feature.properties.NAME === 'Morocco' || 
                     feature.properties.name === 'Morocco' ||
                     feature.properties.NAME_EN === 'Morocco';
    
    return {
      fillColor: isMorocco && showMoroccoHighlight ? moroccoColor : landColor,
      weight: 2,
      opacity: 1,
      color: borderColor,
      fillOpacity: 0.9
    };
  };

  // Fonction appelée lors du survol d'un pays
  const onEachFeature = (feature, layer) => {
    const countryName = feature.properties.NAME || 
                       feature.properties.name || 
                       feature.properties.NAME_EN || 
                       'Pays inconnu';
    
    layer.bindPopup(`<strong>${countryName}</strong>`);
    
    layer.on({
      mouseover: (e) => {
        const layer = e.target;
        layer.setStyle({
          weight: 4,
          color: '#2C3E50',
          fillOpacity: 1
        });
      },
      mouseout: (e) => {
        const layer = e.target;
        layer.setStyle(countryStyle(feature));
      }
    });
  };

  if (loading) {
    return (
      <div style={{ 
        height, 
        width, 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        backgroundColor: '#f8f9fa',
        border: '2px solid #dee2e6',
        borderRadius: '8px'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '18px', marginBottom: '10px' }}>🌍</div>
          <div>Chargement de la carte hors ligne...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ 
        height, 
        width, 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        backgroundColor: '#f8d7da',
        border: '2px solid #f5c6cb',
        borderRadius: '8px',
        color: '#721c24'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '18px', marginBottom: '10px' }}>⚠️</div>
          <div>Erreur: {error}</div>
          <div style={{ fontSize: '12px', marginTop: '5px' }}>
            Vérifiez que les fichiers GeoJSON sont dans le dossier public/data/
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={{ height, width, position: 'relative' }}>
      {/* Indicateur mode hors ligne */}
      <div style={{
        position: 'absolute',
        top: '10px',
        right: '10px',
        zIndex: 1000,
        backgroundColor: 'rgba(0,0,0,0.7)',
        color: 'white',
        padding: '5px 10px',
        borderRadius: '15px',
        fontSize: '12px',
        display: 'flex',
        alignItems: 'center',
        gap: '5px'
      }}>
        📡 Mode hors ligne
      </div>

      <MapContainer
        center={[20, 0]}
        zoom={2}
        style={{ 
          height: '100%', 
          width: '100%',
          backgroundColor: backgroundColor
        }}
        worldCopyJump={true}
        zoomControl={true}
        attributionControl={false}
      >
        {geoJsonData && (
          <GeoJSON
            data={geoJsonData}
            style={countryStyle}
            onEachFeature={onEachFeature}
          />
        )}
      </MapContainer>

      {/* Légende */}
      <div style={{
        position: 'absolute',
        bottom: '10px',
        left: '10px',
        zIndex: 1000,
        backgroundColor: 'rgba(255,255,255,0.9)',
        padding: '10px',
        borderRadius: '5px',
        fontSize: '12px',
        border: '1px solid #ccc'
      }}>
        <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Légende:</div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '5px', marginBottom: '3px' }}>
          <div style={{ width: '15px', height: '15px', backgroundColor: landColor, border: `1px solid ${borderColor}` }}></div>
          <span>Pays</span>
        </div>
        {showMoroccoHighlight && (
          <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
            <div style={{ width: '15px', height: '15px', backgroundColor: moroccoColor, border: `1px solid ${borderColor}` }}></div>
            <span>Maroc</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default OfflineWorldMapComponent;
