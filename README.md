# Composant React - Carte du Monde avec Frontières Correctes du Maroc

Ce projet contient un composant React utilisant Leaflet pour afficher une carte du monde interactive avec les frontières correctes du Maroc.

## Fonctionnalités

- 🗺️ Carte du monde interactive avec Leaflet
- 🇲🇦 Frontières correctes du Maroc
- 🎨 Mise en évidence optionnelle du Maroc
- 📊 Choix entre différents niveaux de détail (détaillée, simplifiée 1, simplifiée 2)
- 📱 Design responsive
- 🖱️ Interaction au survol des pays
- 💡 Popups informatifs
- 📡 **Mode hors ligne** - Fonctionne sans connexion internet !
- 🎨 **Personnalisation des couleurs** en mode hors ligne
- 🚀 **Deux composants** : mixte et 100% hors ligne

## Installation

1. **Cloner ou copier les fichiers dans votre projet React**

2. **Installer les dépendances :**

```bash
npm install
# ou
yarn install
```

3. **Copier les fichiers GeoJSON :**

   - C<PERSON>ez un dossier `public/data/` dans votre projet
   - Copiez les fichiers GeoJSON depuis le projet original :
     - `world_map_detailed.geojson`
     - `world_map_simplified_1.geojson`
     - `world_map_simplified_2.geojson`

4. **Structure des fichiers :**

```
votre-projet/
├── public/
│   └── data/
│       ├── world_map_detailed.geojson
│       ├── world_map_simplified_1.geojson
│       └── world_map_simplified_2.geojson
├── src/
│   ├── WorldMapComponent.jsx
│   ├── App.jsx
│   ├── App.css
│   └── index.js
├── package.json
└── vite.config.js
```

## Utilisation

### Démarrer le serveur de développement :

```bash
npm run dev
# ou
yarn dev
```

### Utilisation du composant :

#### Mode Normal (avec internet) :

```jsx
import WorldMapComponent from "./WorldMapComponent";

<WorldMapComponent
  height="600px"
  width="100%"
  mapStyle="simplified1"
  showMoroccoHighlight={true}
  offlineMode={false}
/>;
```

#### Mode Hors Ligne :

```jsx
import WorldMapComponent from "./WorldMapComponent";

<WorldMapComponent
  height="600px"
  width="100%"
  mapStyle="simplified1"
  showMoroccoHighlight={true}
  offlineMode={true}
  backgroundColor="#87CEEB"
/>;
```

#### Composant 100% Hors Ligne :

```jsx
import OfflineWorldMapComponent from "./OfflineWorldMapComponent";

<OfflineWorldMapComponent
  height="600px"
  width="100%"
  mapStyle="simplified1"
  showMoroccoHighlight={true}
  backgroundColor="#4A90E2"
  landColor="#2ECC71"
  moroccoColor="#E74C3C"
/>;
```

## Props du composant WorldMapComponent

| Prop                   | Type    | Défaut        | Description                                               |
| ---------------------- | ------- | ------------- | --------------------------------------------------------- |
| `height`               | string  | '500px'       | Hauteur de la carte                                       |
| `width`                | string  | '100%'        | Largeur de la carte                                       |
| `mapStyle`             | string  | 'simplified1' | Style de carte ('detailed', 'simplified1', 'simplified2') |
| `showMoroccoHighlight` | boolean | true          | Mettre en évidence le Maroc                               |

## Intégration dans un projet existant

Pour intégrer ce composant dans votre projet React existant :

1. **Copiez le fichier `WorldMapComponent.jsx`** dans votre dossier de composants
2. **Installez les dépendances nécessaires :**

```bash
npm install leaflet react-leaflet
```

3. **Copiez les fichiers GeoJSON** dans `public/data/`
4. **Importez et utilisez le composant :**

```jsx
import WorldMapComponent from "./components/WorldMapComponent";
```

## Personnalisation

### Changer les couleurs :

Modifiez la fonction `countryStyle` dans `WorldMapComponent.jsx` :

```jsx
const countryStyle = (feature) => {
  return {
    fillColor: "#votre-couleur",
    weight: 1,
    opacity: 1,
    color: "#couleur-bordure",
    fillOpacity: 0.6,
  };
};
```

### Ajouter d'autres interactions :

Modifiez la fonction `onEachFeature` pour ajouter des événements personnalisés.

## Technologies utilisées

- **React** - Framework JavaScript
- **Leaflet** - Bibliothèque de cartes interactives
- **React-Leaflet** - Composants React pour Leaflet
- **Vite** - Outil de build rapide

## Crédits

- Données cartographiques : Mahmoud Zakaria ([www.mahmoud.ma](https://www.mahmoud.ma/))
- Outil de simplification : [Mapshaper](https://mapshaper.org/)

## Licence

MIT
