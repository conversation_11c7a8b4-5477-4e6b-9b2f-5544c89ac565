import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, GeoJSON } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';

const WorldMapComponent = ({ 
  height = '500px', 
  width = '100%',
  mapStyle = 'simplified', // 'detailed', 'simplified1', 'simplified2'
  showMoroccoHighlight = true 
}) => {
  const [geoJsonData, setGeoJsonData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fonction pour charger les données GeoJSON
  const loadGeoJsonData = async () => {
    try {
      setLoading(true);
      let fileName;
      
      switch (mapStyle) {
        case 'detailed':
          fileName = 'world_map_detailed.geojson';
          break;
        case 'simplified1':
          fileName = 'world_map_simplified_1.geojson';
          break;
        case 'simplified2':
          fileName = 'world_map_simplified_2.geojson';
          break;
        default:
          fileName = 'world_map_simplified_1.geojson';
      }

      // Vous devrez ajuster ce chemin selon votre structure de projet
      const response = await fetch(`/data/${fileName}`);
      
      if (!response.ok) {
        throw new Error(`Erreur lors du chargement: ${response.status}`);
      }
      
      const data = await response.json();
      setGeoJsonData(data);
    } catch (err) {
      setError(err.message);
      console.error('Erreur lors du chargement des données GeoJSON:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadGeoJsonData();
  }, [mapStyle]);

  // Style pour les pays
  const countryStyle = (feature) => {
    const isMorocco = feature.properties.NAME === 'Morocco' || 
                     feature.properties.name === 'Morocco' ||
                     feature.properties.NAME_EN === 'Morocco';
    
    return {
      fillColor: isMorocco && showMoroccoHighlight ? '#ff6b6b' : '#74b9ff',
      weight: 1,
      opacity: 1,
      color: '#2d3436',
      fillOpacity: isMorocco && showMoroccoHighlight ? 0.8 : 0.6
    };
  };

  // Fonction appelée lors du survol d'un pays
  const onEachFeature = (feature, layer) => {
    const countryName = feature.properties.NAME || 
                       feature.properties.name || 
                       feature.properties.NAME_EN || 
                       'Pays inconnu';
    
    layer.bindPopup(`<strong>${countryName}</strong>`);
    
    layer.on({
      mouseover: (e) => {
        const layer = e.target;
        layer.setStyle({
          weight: 3,
          color: '#2d3436',
          fillOpacity: 0.9
        });
      },
      mouseout: (e) => {
        const layer = e.target;
        layer.setStyle(countryStyle(feature));
      }
    });
  };

  if (loading) {
    return (
      <div style={{ 
        height, 
        width, 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        backgroundColor: '#f8f9fa',
        border: '1px solid #dee2e6',
        borderRadius: '4px'
      }}>
        <div>Chargement de la carte...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ 
        height, 
        width, 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        backgroundColor: '#f8d7da',
        border: '1px solid #f5c6cb',
        borderRadius: '4px',
        color: '#721c24'
      }}>
        <div>Erreur: {error}</div>
      </div>
    );
  }

  return (
    <div style={{ height, width }}>
      <MapContainer
        center={[20, 0]} // Centre du monde
        zoom={2}
        style={{ height: '100%', width: '100%' }}
        worldCopyJump={true}
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        />
        
        {geoJsonData && (
          <GeoJSON
            data={geoJsonData}
            style={countryStyle}
            onEachFeature={onEachFeature}
          />
        )}
      </MapContainer>
    </div>
  );
};

export default WorldMapComponent;
