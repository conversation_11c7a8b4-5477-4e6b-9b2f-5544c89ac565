{"name": "morocco-world-map-react", "version": "1.0.0", "description": "Composant React avec Leaflet pour afficher la carte du monde avec les frontières correctes du Maroc", "main": "src/index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "leaflet": "^1.9.4", "react-leaflet": "^4.2.1"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "vite": "^4.4.5"}, "keywords": ["react", "leaflet", "morocco", "world-map", "g<PERSON><PERSON><PERSON>", "cartography"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT"}