#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🌍 Configuration du Mode Hors Ligne pour la Carte du Monde');
console.log('=========================================================');

// Vérifier si nous sommes dans le bon répertoire
const currentDir = process.cwd();
const sourceDir = path.join(currentDir, 'morocco-complete-map-geojson-topojson-main');

if (!fs.existsSync(sourceDir)) {
  console.error('❌ Erreur: Le dossier "morocco-complete-map-geojson-topojson-main" n\'a pas été trouvé.');
  console.log('💡 Assurez-vous d\'être dans le bon répertoire.');
  process.exit(1);
}

// Créer le dossier public/data s'il n'existe pas
const publicDataDir = path.join(currentDir, 'public', 'data');
if (!fs.existsSync(path.join(currentDir, 'public'))) {
  fs.mkdirSync(path.join(currentDir, 'public'));
  console.log('📁 Dossier "public" créé');
}

if (!fs.existsSync(publicDataDir)) {
  fs.mkdirSync(publicDataDir);
  console.log('📁 Dossier "public/data" créé');
}

// Liste des fichiers à copier
const filesToCopy = [
  'world_map_detailed.geojson',
  'world_map_simplified_1.geojson',
  'world_map_simplified_2.geojson'
];

console.log('\n📋 Copie des fichiers GeoJSON...');

let copiedFiles = 0;
filesToCopy.forEach(fileName => {
  const sourcePath = path.join(sourceDir, fileName);
  const destPath = path.join(publicDataDir, fileName);
  
  if (fs.existsSync(sourcePath)) {
    try {
      fs.copyFileSync(sourcePath, destPath);
      console.log(`✅ ${fileName} copié avec succès`);
      copiedFiles++;
    } catch (error) {
      console.error(`❌ Erreur lors de la copie de ${fileName}:`, error.message);
    }
  } else {
    console.warn(`⚠️  ${fileName} non trouvé dans le dossier source`);
  }
});

console.log(`\n📊 Résumé: ${copiedFiles}/${filesToCopy.length} fichiers copiés`);

// Vérifier les tailles des fichiers
console.log('\n📏 Tailles des fichiers:');
filesToCopy.forEach(fileName => {
  const filePath = path.join(publicDataDir, fileName);
  if (fs.existsSync(filePath)) {
    const stats = fs.statSync(filePath);
    const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
    console.log(`   ${fileName}: ${sizeInMB} MB`);
  }
});

// Vérifier si package.json existe
const packageJsonPath = path.join(currentDir, 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.log('\n⚠️  package.json non trouvé. Création en cours...');
  
  const packageJson = {
    "name": "morocco-world-map-offline",
    "version": "1.0.0",
    "description": "Carte du monde hors ligne avec frontières correctes du Maroc",
    "main": "src/main.jsx",
    "scripts": {
      "dev": "vite",
      "build": "vite build",
      "preview": "vite preview"
    },
    "dependencies": {
      "react": "^18.2.0",
      "react-dom": "^18.2.0",
      "leaflet": "^1.9.4",
      "react-leaflet": "^4.2.1"
    },
    "devDependencies": {
      "@vitejs/plugin-react": "^4.0.3",
      "vite": "^4.4.5"
    }
  };
  
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  console.log('✅ package.json créé');
}

console.log('\n🎉 Configuration terminée !');
console.log('\n📋 Prochaines étapes:');
console.log('1. Installer les dépendances: npm install');
console.log('2. Démarrer le serveur: npm run dev');
console.log('3. Ouvrir http://localhost:3000');
console.log('\n💡 Conseils:');
console.log('- Utilisez "simplified1" pour de meilleures performances');
console.log('- Activez le mode hors ligne pour fonctionner sans internet');
console.log('- Testez la déconnexion réseau pour vérifier le mode hors ligne');

console.log('\n🌍 Votre carte du monde hors ligne est prête !');
